# 🎮 Claw Machine Game

A fun 2D web-based claw machine game built with Matter.js physics engine!

## 🎯 How to Play

1. **Move the Claw**: Use the arrow keys (← →) or click the "Move Left" and "Move Right" buttons to position the claw horizontally.

2. **Drop the Claw**: Press the SPACE bar or click the "Drop Claw" button to make the claw descend and attempt to grab a prize.

3. **Grab Prizes**: The claw will automatically close when it reaches the bottom and try to grab any nearby prizes.

4. **Collect Points**: Successfully lifted prizes will be collected when the claw returns to the top, adding points to your score.

## 🎨 Features

- **Realistic Physics**: Built with Matter.js for authentic claw machine physics
- **Visual Effects**: Particle effects and sparkles when grabbing prizes
- **Sound Effects**: Audio feedback for claw movements, grabbing, and successful collections
- **Variety of Prizes**: Different shapes, sizes, and point values
- **Responsive Controls**: Both keyboard and button controls supported

## 🎵 Audio

The game includes procedurally generated sound effects:
- Claw movement sounds
- Prize grabbing audio
- Success celebration chords

*Note: Click anywhere on the page first to enable audio (browser security requirement)*

## 🚀 Getting Started

Simply open `index.html` in your web browser to start playing!

## 🛠️ Technical Details

- **Physics Engine**: Matter.js
- **Graphics**: HTML5 Canvas
- **Audio**: Web Audio API
- **No Dependencies**: Pure vanilla JavaScript

## 🎮 Controls Summary

| Control | Action |
|---------|--------|
| ← → Arrow Keys | Move claw left/right |
| SPACE | Drop claw |
| Mouse/Touch | Use on-screen buttons |

Have fun playing! 🎉
