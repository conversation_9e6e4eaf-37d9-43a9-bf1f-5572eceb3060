<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Claw Machine Game</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Arial', sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            overflow: hidden;
        }

        .game-container {
            background: #2c3e50;
            border-radius: 20px;
            padding: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border: 5px solid #34495e;
        }

        #gameCanvas {
            border: 3px solid #e74c3c;
            border-radius: 10px;
            background: #ecf0f1;
            display: block;
        }

        .controls {
            margin-top: 15px;
            text-align: center;
            color: #ecf0f1;
        }

        .control-button {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .control-button:hover {
            background: #c0392b;
        }

        .control-button:active {
            transform: scale(0.95);
        }

        .score {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #f39c12;
        }

        .instructions {
            font-size: 14px;
            margin-top: 10px;
            color: #bdc3c7;
        }

        .game-title {
            text-align: center;
            color: #f39c12;
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }
    </style>
</head>
<body>
    <div class="game-container">
        <h1 class="game-title">🎮 CLAW MACHINE 🎮</h1>
        
        <div class="score">Score: <span id="scoreValue">0</span></div>
        
        <canvas id="gameCanvas" width="800" height="600"></canvas>
        
        <div class="controls">
            <button class="control-button" id="leftBtn">← Move Left</button>
            <button class="control-button" id="dropBtn">⬇ Drop Claw</button>
            <button class="control-button" id="rightBtn">Move Right →</button>
        </div>
        
        <div class="instructions">
            Use arrow keys or buttons to move the claw. Press SPACE or Drop button to grab prizes!
        </div>
    </div>

    <!-- Matter.js Physics Engine -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/matter-js/0.19.0/matter.min.js"></script>
    
    <script src="game.js"></script>
</body>
</html>
