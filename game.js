// Matter.js modules
const {
  Engine,
  Render,
  Runner,
  Bodies,
  Body,
  Composite,
  Constraint,
  Mouse,
  MouseConstraint,
  Events,
} = Matter;

class ClawMachine {
  constructor() {
    this.canvas = document.getElementById("gameCanvas");
    this.scoreElement = document.getElementById("scoreValue");
    this.score = 0;

    // Game state
    this.gameState = "ready"; // ready, dropping, grabbing, returning
    this.clawPosition = 400; // horizontal position
    this.clawSpeed = 3;
    this.isClawOpen = true;

    // Visual effects
    this.particles = [];
    this.sparkles = [];

    // Audio context for sound effects
    this.audioContext = null;
    this.initAudio();

    this.initPhysics();
    this.createWorld();
    this.setupControls();
    this.startGame();
  }

  initPhysics() {
    // Create engine
    this.engine = Engine.create();
    this.world = this.engine.world;

    // Create renderer
    this.render = Render.create({
      canvas: this.canvas,
      engine: this.engine,
      options: {
        width: 800,
        height: 600,
        wireframes: false,
        background: "#87CEEB",
        showAngleIndicator: false,
        showVelocity: false,
      },
    });

    // Create runner
    this.runner = Runner.create();

    // Disable gravity initially for claw movement
    this.engine.world.gravity.y = 0.8;
  }

  createWorld() {
    // Machine boundaries
    const walls = [
      // Floor
      Bodies.rectangle(400, 580, 800, 40, {
        isStatic: true,
        render: { fillStyle: "#8B4513" },
      }),
      // Left wall
      Bodies.rectangle(20, 300, 40, 600, {
        isStatic: true,
        render: { fillStyle: "#654321" },
      }),
      // Right wall
      Bodies.rectangle(780, 300, 40, 600, {
        isStatic: true,
        render: { fillStyle: "#654321" },
      }),
      // Top boundary (invisible)
      Bodies.rectangle(400, 20, 800, 40, {
        isStatic: true,
        render: { fillStyle: "transparent" },
      }),
    ];

    // Glass front (visual only)
    const glassFront = Bodies.rectangle(400, 300, 760, 560, {
      isStatic: true,
      isSensor: true,
      render: {
        fillStyle: "rgba(173, 216, 230, 0.3)",
        strokeStyle: "#4682B4",
        lineWidth: 2,
      },
    });

    // Create claw mechanism
    this.createClaw();

    // Add walls to world
    Composite.add(this.world, [...walls, glassFront]);
  }

  createClaw() {
    // Claw rail (invisible constraint point)
    this.clawRail = Bodies.rectangle(this.clawPosition, 80, 20, 20, {
      isStatic: true,
      render: { fillStyle: "transparent" },
    });

    // Chain links
    this.chainLinks = [];
    const linkCount = 8;
    const linkHeight = 15;

    for (let i = 0; i < linkCount; i++) {
      const link = Bodies.rectangle(
        this.clawPosition,
        100 + i * linkHeight,
        8,
        linkHeight,
        {
          render: { fillStyle: "#696969" },
          frictionAir: 0.02,
        }
      );
      this.chainLinks.push(link);
    }

    // Claw body
    this.clawBody = Bodies.rectangle(this.clawPosition, 220, 30, 20, {
      render: {
        fillStyle: "#FFD700",
        strokeStyle: "#DAA520",
        lineWidth: 2,
      },
      frictionAir: 0.05,
    });

    // Claw arms (left and right)
    this.leftClaw = Bodies.rectangle(this.clawPosition - 15, 235, 8, 25, {
      render: {
        fillStyle: "#FFA500",
        strokeStyle: "#FF8C00",
        lineWidth: 1,
      },
    });

    this.rightClaw = Bodies.rectangle(this.clawPosition + 15, 235, 8, 25, {
      render: {
        fillStyle: "#FFA500",
        strokeStyle: "#FF8C00",
        lineWidth: 1,
      },
    });

    // Add all claw parts to world
    Composite.add(this.world, [
      this.clawRail,
      ...this.chainLinks,
      this.clawBody,
      this.leftClaw,
      this.rightClaw,
    ]);

    // Create constraints for chain
    this.createChainConstraints();
    this.createClawConstraints();
  }

  createChainConstraints() {
    // Connect rail to first chain link
    const railConstraint = Constraint.create({
      bodyA: this.clawRail,
      bodyB: this.chainLinks[0],
      length: 20,
      stiffness: 0.8,
    });

    // Connect chain links together
    this.chainConstraints = [railConstraint];

    for (let i = 0; i < this.chainLinks.length - 1; i++) {
      const constraint = Constraint.create({
        bodyA: this.chainLinks[i],
        bodyB: this.chainLinks[i + 1],
        length: 15,
        stiffness: 0.8,
      });
      this.chainConstraints.push(constraint);
    }

    // Connect last chain link to claw body
    const clawConstraint = Constraint.create({
      bodyA: this.chainLinks[this.chainLinks.length - 1],
      bodyB: this.clawBody,
      length: 20,
      stiffness: 0.8,
    });
    this.chainConstraints.push(clawConstraint);

    // Add all constraints to world
    Composite.add(this.world, this.chainConstraints);
  }

  createClawConstraints() {
    // Connect claw arms to claw body
    this.leftClawConstraint = Constraint.create({
      bodyA: this.clawBody,
      bodyB: this.leftClaw,
      pointA: { x: -10, y: 10 },
      pointB: { x: 0, y: -10 },
      length: 5,
      stiffness: 0.9,
    });

    this.rightClawConstraint = Constraint.create({
      bodyA: this.clawBody,
      bodyB: this.rightClaw,
      pointA: { x: 10, y: 10 },
      pointB: { x: 0, y: -10 },
      length: 5,
      stiffness: 0.9,
    });

    Composite.add(this.world, [
      this.leftClawConstraint,
      this.rightClawConstraint,
    ]);
  }

  createPrizes() {
    this.prizes = [];

    // Create various prize shapes and sizes
    const prizeTypes = [
      { width: 25, height: 25, color: "#FF6B6B", points: 10 }, // Small red cube
      { width: 35, height: 20, color: "#4ECDC4", points: 15 }, // Medium teal rectangle
      { width: 20, height: 40, color: "#45B7D1", points: 20 }, // Tall blue rectangle
      { width: 30, height: 30, color: "#96CEB4", points: 25 }, // Green square
      { width: 40, height: 15, color: "#FFEAA7", points: 30 }, // Wide yellow rectangle
    ];

    // Create random prizes
    for (let i = 0; i < 12; i++) {
      const prizeType =
        prizeTypes[Math.floor(Math.random() * prizeTypes.length)];
      const x = 100 + Math.random() * 600;
      const y = 400 + Math.random() * 100;

      const prize = Bodies.rectangle(x, y, prizeType.width, prizeType.height, {
        render: { fillStyle: prizeType.color },
        restitution: 0.3,
        friction: 0.8,
        frictionAir: 0.01,
      });

      // Store prize value
      prize.prizeValue = prizeType.points;
      prize.isPrize = true;

      this.prizes.push(prize);
    }

    // Add some circular prizes
    for (let i = 0; i < 6; i++) {
      const x = 100 + Math.random() * 600;
      const y = 450 + Math.random() * 80;
      const radius = 15 + Math.random() * 10;

      const prize = Bodies.circle(x, y, radius, {
        render: { fillStyle: "#E17055" },
        restitution: 0.4,
        friction: 0.7,
        frictionAir: 0.01,
      });

      prize.prizeValue = Math.floor(radius * 2);
      prize.isPrize = true;

      this.prizes.push(prize);
    }

    Composite.add(this.world, this.prizes);
  }

  setupControls() {
    // Keyboard controls
    document.addEventListener("keydown", (event) => {
      switch (event.code) {
        case "ArrowLeft":
          this.moveClawLeft();
          break;
        case "ArrowRight":
          this.moveClawRight();
          break;
        case "Space":
          event.preventDefault();
          this.dropClaw();
          break;
      }
    });

    // Button controls
    document
      .getElementById("leftBtn")
      .addEventListener("click", () => this.moveClawLeft());
    document
      .getElementById("rightBtn")
      .addEventListener("click", () => this.moveClawRight());
    document
      .getElementById("dropBtn")
      .addEventListener("click", () => this.dropClaw());
  }

  moveClawLeft() {
    if (this.gameState === "ready" && this.clawPosition > 60) {
      this.clawPosition -= this.clawSpeed;
      this.updateClawPosition();
    }
  }

  moveClawRight() {
    if (this.gameState === "ready" && this.clawPosition < 740) {
      this.clawPosition += this.clawSpeed;
      this.updateClawPosition();
    }
  }

  updateClawPosition() {
    Body.setPosition(this.clawRail, { x: this.clawPosition, y: 80 });
  }

  dropClaw() {
    if (this.gameState === "ready") {
      this.gameState = "dropping";
      this.playClawSound();
      this.startClawDrop();
    }
  }

  startClawDrop() {
    // Enable gravity for claw mechanism
    this.chainLinks.forEach((link) => {
      Body.setStatic(link, false);
    });
    Body.setStatic(this.clawBody, false);
    Body.setStatic(this.leftClaw, false);
    Body.setStatic(this.rightClaw, false);

    // Add downward force to claw
    setTimeout(() => {
      if (this.gameState === "dropping") {
        Body.applyForce(this.clawBody, this.clawBody.position, {
          x: 0,
          y: 0.02,
        });
      }
    }, 100);

    // Close claw after a delay
    setTimeout(() => {
      this.closeClaw();
    }, 1500);

    // Return claw after grabbing
    setTimeout(() => {
      this.returnClaw();
    }, 3000);
  }

  closeClaw() {
    if (this.gameState === "dropping") {
      this.gameState = "grabbing";
      this.isClawOpen = false;

      // Move claw arms closer together
      Body.setPosition(this.leftClaw, {
        x: this.clawBody.position.x - 5,
        y: this.leftClaw.position.y,
      });
      Body.setPosition(this.rightClaw, {
        x: this.clawBody.position.x + 5,
        y: this.rightClaw.position.y,
      });

      // Check for grabbed prizes
      this.checkGrabbedPrizes();
    }
  }

  checkGrabbedPrizes() {
    const clawX = this.clawBody.position.x;
    const clawY = this.clawBody.position.y;

    this.prizes.forEach((prize) => {
      const distance = Math.sqrt(
        Math.pow(prize.position.x - clawX, 2) +
          Math.pow(prize.position.y - clawY, 2)
      );

      // If prize is close enough to claw, attach it
      if (distance < 40) {
        const constraint = Constraint.create({
          bodyA: this.clawBody,
          bodyB: prize,
          length: distance,
          stiffness: 0.5,
        });

        Composite.add(this.world, constraint);
        prize.isGrabbed = true;
        prize.grabConstraint = constraint;

        // Create sparkle effect when prize is grabbed
        this.createSparkleEffect(prize.position.x, prize.position.y);
        this.playGrabSound();
      }
    });
  }

  returnClaw() {
    this.gameState = "returning";

    // Apply upward force to return claw
    const returnForce = { x: 0, y: -0.03 };
    Body.applyForce(this.clawBody, this.clawBody.position, returnForce);

    // Check if claw reached top
    const checkReturn = setInterval(() => {
      if (this.clawBody.position.y < 150) {
        clearInterval(checkReturn);
        this.resetClaw();
      }
    }, 100);
  }

  resetClaw() {
    // Check for collected prizes
    this.collectPrizes();

    // Reset claw state
    this.gameState = "ready";
    this.isClawOpen = true;

    // Reset claw position
    Body.setPosition(this.clawBody, { x: this.clawPosition, y: 220 });
    Body.setPosition(this.leftClaw, { x: this.clawPosition - 15, y: 235 });
    Body.setPosition(this.rightClaw, { x: this.clawPosition + 15, y: 235 });

    // Reset chain links
    this.chainLinks.forEach((link, index) => {
      Body.setPosition(link, {
        x: this.clawPosition,
        y: 100 + index * 15,
      });
    });

    // Stop all movement
    Body.setVelocity(this.clawBody, { x: 0, y: 0 });
    Body.setAngularVelocity(this.clawBody, 0);
    this.chainLinks.forEach((link) => {
      Body.setVelocity(link, { x: 0, y: 0 });
      Body.setAngularVelocity(link, 0);
    });
  }

  collectPrizes() {
    this.prizes.forEach((prize, index) => {
      if (prize.isGrabbed && prize.position.y < 100) {
        // Prize was successfully collected
        this.score += prize.prizeValue;
        this.updateScore();

        // Remove prize and its constraint
        if (prize.grabConstraint) {
          Composite.remove(this.world, prize.grabConstraint);
        }
        Composite.remove(this.world, prize);
        this.prizes.splice(index, 1);
      } else if (prize.isGrabbed) {
        // Prize was dropped, remove constraint
        if (prize.grabConstraint) {
          Composite.remove(this.world, prize.grabConstraint);
        }
        prize.isGrabbed = false;
        prize.grabConstraint = null;
      }
    });
  }

  updateScore() {
    this.scoreElement.textContent = this.score;

    // Create celebration particles when score increases
    this.createCelebrationParticles();
  }

  createCelebrationParticles() {
    const colors = ["#FFD700", "#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4"];

    for (let i = 0; i < 10; i++) {
      const particle = {
        x: 400 + (Math.random() - 0.5) * 200,
        y: 100 + Math.random() * 100,
        vx: (Math.random() - 0.5) * 4,
        vy: -Math.random() * 3 - 1,
        color: colors[Math.floor(Math.random() * colors.length)],
        life: 1.0,
        decay: 0.02,
      };
      this.particles.push(particle);
    }
  }

  updateParticles() {
    this.particles = this.particles.filter((particle) => {
      particle.x += particle.vx;
      particle.y += particle.vy;
      particle.vy += 0.1; // gravity
      particle.life -= particle.decay;
      return particle.life > 0;
    });
  }

  renderParticles() {
    const ctx = this.render.canvas.getContext("2d");

    this.particles.forEach((particle) => {
      ctx.save();
      ctx.globalAlpha = particle.life;
      ctx.fillStyle = particle.color;
      ctx.beginPath();
      ctx.arc(particle.x, particle.y, 3, 0, Math.PI * 2);
      ctx.fill();
      ctx.restore();
    });
  }

  createSparkleEffect(x, y) {
    for (let i = 0; i < 5; i++) {
      const sparkle = {
        x: x + (Math.random() - 0.5) * 30,
        y: y + (Math.random() - 0.5) * 30,
        size: Math.random() * 3 + 1,
        life: 1.0,
        decay: 0.05,
        twinkle: Math.random() * Math.PI * 2,
      };
      this.sparkles.push(sparkle);
    }
  }

  updateSparkles() {
    this.sparkles = this.sparkles.filter((sparkle) => {
      sparkle.life -= sparkle.decay;
      sparkle.twinkle += 0.2;
      return sparkle.life > 0;
    });
  }

  renderSparkles() {
    const ctx = this.render.canvas.getContext("2d");

    this.sparkles.forEach((sparkle) => {
      const alpha = sparkle.life * (0.5 + 0.5 * Math.sin(sparkle.twinkle));
      ctx.save();
      ctx.globalAlpha = alpha;
      ctx.fillStyle = "#FFFFFF";
      ctx.shadowColor = "#FFFFFF";
      ctx.shadowBlur = 5;
      ctx.beginPath();
      ctx.arc(sparkle.x, sparkle.y, sparkle.size, 0, Math.PI * 2);
      ctx.fill();
      ctx.restore();
    });
  }

  initAudio() {
    try {
      this.audioContext = new (window.AudioContext ||
        window.webkitAudioContext)();
    } catch (e) {
      console.log("Web Audio API not supported");
    }
  }

  playSound(frequency, duration, type = "sine") {
    if (!this.audioContext) return;

    const oscillator = this.audioContext.createOscillator();
    const gainNode = this.audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(this.audioContext.destination);

    oscillator.frequency.setValueAtTime(
      frequency,
      this.audioContext.currentTime
    );
    oscillator.type = type;

    gainNode.gain.setValueAtTime(0.1, this.audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(
      0.01,
      this.audioContext.currentTime + duration
    );

    oscillator.start(this.audioContext.currentTime);
    oscillator.stop(this.audioContext.currentTime + duration);
  }

  playClawSound() {
    this.playSound(200, 0.3, "square");
  }

  playGrabSound() {
    this.playSound(400, 0.2, "triangle");
  }

  playSuccessSound() {
    // Play a happy chord
    this.playSound(523, 0.5); // C
    setTimeout(() => this.playSound(659, 0.5), 100); // E
    setTimeout(() => this.playSound(784, 0.5), 200); // G
  }

  startGame() {
    // Create prizes
    this.createPrizes();

    // Start physics engine
    Render.run(this.render);
    Runner.run(this.runner, this.engine);

    // Game loop
    this.gameLoop();
  }

  gameLoop() {
    // Update game state
    requestAnimationFrame(() => this.gameLoop());

    // Update visual effects
    this.updateParticles();
    this.updateSparkles();

    // Render visual effects on top of physics
    this.renderParticles();
    this.renderSparkles();

    // Add any continuous game logic here
    if (this.gameState === "returning" && this.clawBody.position.y < 150) {
      // Ensure claw returns to top
      Body.setPosition(this.clawBody, {
        x: this.clawBody.position.x,
        y: Math.max(this.clawBody.position.y, 150),
      });
    }
  }
}

// Initialize game when page loads
window.addEventListener("load", () => {
  new ClawMachine();
});
